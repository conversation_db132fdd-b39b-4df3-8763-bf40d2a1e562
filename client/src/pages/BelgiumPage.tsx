import { useEffect } from "react";
import Navbar from "@/components/Navbar";
import Hero from "@/components/Hero";
import SocialProof from "@/components/SocialProof";
import ProblemSolution from "@/components/ProblemSolution";
import FeatureTrio from "@/components/FeatureTrio";
import RoiCalculator from "@/components/RoiCalculator";
import Testimonials from "@/components/Testimonials";
import IndustryInsights from "@/components/IndustryInsights";
import Pricing from "@/components/Pricing";
import SecurityBanner from "@/components/SecurityBanner";
import Footer from "@/components/Footer";
import CookieNotice from "@/components/CookieNotice";
import LanguageRedirect from "@/components/LanguageRedirect";
import { getTranslations } from "@/utils/translations";

export default function BelgiumPage() {
  // Set page title for Belgium
  useEffect(() => {
    document.title = "AiLex - AI Legal Associate for Belgian Lawyers";
  }, []);

  const t = getTranslations("en");

  return (
    <LanguageRedirect>
      <div className="relative overflow-x-hidden">
        <Navbar currentState="BE" language="en" />
        <main>
          <Hero
            customTitle={t.heroTitle}
            customSubtitle={t.heroSubtitle}
            state="BE"
          />
          <SocialProof />
          <ProblemSolution />
          <FeatureTrio state="BE" />
          <RoiCalculator />
          <IndustryInsights />
          <Testimonials defaultState="BE" />
          <Pricing />
          <SecurityBanner />
        </main>
        <Footer />
        <CookieNotice />
      </div>
    </LanguageRedirect>
  );
}
