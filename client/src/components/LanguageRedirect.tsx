import { useEffect, useState } from "react";
import { useLocation } from "wouter";
import {
  detectPreferredLanguage,
  getLanguageFromPath,
  getRouteForLanguage,
  setLanguageCookie,
  BelgianLanguage,
} from "@/utils/languageDetection";

interface LanguageRedirectProps {
  children: React.ReactNode;
}

export default function LanguageRedirect({ children }: LanguageRedirectProps) {
  const [location, setLocation] = useLocation();
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [hasChecked, setHasChecked] = useState(false);

  useEffect(() => {
    async function handleLanguageRedirection() {
      // Only run on client side
      if (typeof window === "undefined") return;

      // Don't run if we've already checked
      if (hasChecked) return;

      const currentPath = location;
      const currentLanguage = getLanguageFromPath(currentPath);

      // If user is on a specific language version, update cookie and don't redirect
      if (currentLanguage && currentPath !== "/be") {
        setLanguageCookie(currentLanguage);
        setHasChecked(true);
        return;
      }

      // Only redirect if user is on the base /be route
      if (currentPath === "/be" || currentPath === "/be/") {
        setIsRedirecting(true);

        try {
          const preference = await detectPreferredLanguage();
          const targetRoute = getRouteForLanguage("/be", preference.language);

          // Set cookie to remember the preference
          setLanguageCookie(preference.language);

          // Log for debugging (remove in production)
          console.log(
            `Language detection: ${preference.source} -> ${preference.language}`
          );

          // Redirect to the appropriate language version
          window.location.href = targetRoute;
        } catch (error) {
          console.error("Language detection failed:", error);
          // Fallback to English
          setIsRedirecting(false);
        }
      } else {
        setHasChecked(true);
      }
    }

    // Small delay to ensure router is ready
    const timer = setTimeout(handleLanguageRedirection, 100);
    return () => clearTimeout(timer);
  }, [location, hasChecked]);

  // Show loading state while redirecting
  if (isRedirecting) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Detecting your preferred language...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

// Higher-order component for pages that need language redirection
export function withLanguageRedirect<P extends object>(
  WrappedComponent: React.ComponentType<P>
) {
  const ComponentWithLanguageRedirect = (props: P) => {
    return (
      <LanguageRedirect>
        <WrappedComponent {...props} />
      </LanguageRedirect>
    );
  };

  ComponentWithLanguageRedirect.displayName = `withLanguageRedirect(${
    WrappedComponent.displayName || WrappedComponent.name
  })`;

  return ComponentWithLanguageRedirect;
}
