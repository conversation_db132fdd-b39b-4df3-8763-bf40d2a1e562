import React from "react";
import { useLocation } from "wouter";
import {
  getLanguageFromPath,
  setLanguageCookie,
} from "@/utils/languageDetection";

interface CountryLanguageSelectorProps {
  className?: string;
}

export default function CountryLanguageSelector({
  className = "",
}: CountryLanguageSelectorProps) {
  const [location] = useLocation();

  // Get current selection based on the current route
  const getCurrentSelection = () => {
    if (location.startsWith("/be-fr")) return "BE-FR";
    if (location.startsWith("/be-nl")) return "BE-NL";
    if (location.startsWith("/be")) return "BE-EN";
    if (location.startsWith("/tx")) return "TX";
    if (location.startsWith("/fl")) return "FL";
    if (location.startsWith("/ny")) return "NY";
    return "BE-EN"; // Default fallback
  };

  const handleSelectionChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;

    // Handle Belgian language variants
    if (value === "BE-EN") {
      setLanguageCookie("en");
      window.location.href = "/be";
    } else if (value === "BE-FR") {
      setLanguageCookie("fr");
      window.location.href = "/be-fr";
    } else if (value === "BE-NL") {
      setLanguageCookie("nl");
      window.location.href = "/be-nl";
    } else {
      // Handle other states
      window.location.href = `/${value.toLowerCase()}`;
    }
  };

  return (
    <div className={`fixed top-20 right-4 z-30 md:block hidden ${className}`}>
      <div className="bg-white rounded-lg shadow-md p-2">
        <select
          value={getCurrentSelection()}
          onChange={handleSelectionChange}
          className="text-sm font-medium outline-none cursor-pointer"
          aria-label="Select country and language"
        >
          <optgroup label="🇧🇪 Belgium">
            <option value="BE-EN">BE-EN (English)</option>
            <option value="BE-FR">BE-FR (Français)</option>
            <option value="BE-NL">BE-NL (Nederlands)</option>
          </optgroup>
          <optgroup label="🇺🇸 United States">
            <option value="TX">Texas</option>
            <option value="FL">Florida</option>
            <option value="NY">New York</option>
          </optgroup>
        </select>
      </div>
    </div>
  );
}
