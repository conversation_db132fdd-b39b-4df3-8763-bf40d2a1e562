import React, { useState, useEffect, useRef } from "react";
import { useLocation } from "wouter";
import { ChevronDown, Globe } from "lucide-react";
import {
  BelgianLanguage,
  LANGUAGE_NAMES,
  LANGUAGE_FLAGS,
  getLanguageFromPath,
  getRouteForLanguage,
  setLanguageCookie,
} from "@/utils/languageDetection";

interface LanguageSwitcherProps {
  className?: string;
}

export default function LanguageSwitcher({
  className = "",
}: LanguageSwitcherProps) {
  const [location, setLocation] = useLocation();
  const [isOpen, setIsOpen] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState<BelgianLanguage>("en");
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Detect current language from URL
  useEffect(() => {
    const detectedLanguage = getLanguageFromPath(location) || "en";
    setCurrentLanguage(detectedLanguage);
  }, [location]);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleLanguageChange = (language: BelgianLanguage) => {
    if (language === currentLanguage) {
      setIsOpen(false);
      return;
    }

    // Set cookie to remember preference
    setLanguageCookie(language);

    // Get the new route for the selected language
    const newRoute = getRouteForLanguage(location, language);

    // Navigate to the new route
    window.location.href = newRoute;
    setIsOpen(false);
  };

  const languages: BelgianLanguage[] = ["en", "fr", "nl"];

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Trigger Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-gray-300 transition-colors duration-200 shadow-sm"
        aria-label="Select language"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <Globe className="w-4 h-4" />
        <span className="hidden sm:inline">
          {LANGUAGE_FLAGS[currentLanguage]} {LANGUAGE_NAMES[currentLanguage]}
        </span>
        <span className="sm:hidden">{LANGUAGE_FLAGS[currentLanguage]}</span>
        <ChevronDown
          className={`w-4 h-4 transition-transform duration-200 ${
            isOpen ? "rotate-180" : ""
          }`}
        />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute right-0 top-full mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50 overflow-hidden">
          <div className="py-1">
            {languages.map((language) => (
              <button
                key={language}
                onClick={() => handleLanguageChange(language)}
                className={`w-full flex items-center gap-3 px-4 py-3 text-sm text-left hover:bg-gray-50 transition-colors duration-150 ${
                  currentLanguage === language
                    ? "bg-blue-50 text-blue-700 font-medium"
                    : "text-gray-700"
                }`}
                role="menuitem"
              >
                <span className="text-lg">{LANGUAGE_FLAGS[language]}</span>
                <span className="flex-1">{LANGUAGE_NAMES[language]}</span>
                {currentLanguage === language && (
                  <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                )}
              </button>
            ))}
          </div>

          {/* Footer note */}
          <div className="border-t border-gray-100 px-4 py-2">
            <p className="text-xs text-gray-500">
              Language preference is saved
            </p>
          </div>
        </div>
      )}
    </div>
  );
}

// Hook for using language switcher in other components
export function useCurrentLanguage(): BelgianLanguage {
  const [location] = useLocation();
  const [currentLanguage, setCurrentLanguage] = useState<BelgianLanguage>("en");

  useEffect(() => {
    const detectedLanguage = getLanguageFromPath(location) || "en";
    setCurrentLanguage(detectedLanguage);
  }, [location]);

  return currentLanguage;
}
