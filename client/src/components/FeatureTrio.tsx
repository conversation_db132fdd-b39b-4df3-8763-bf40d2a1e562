import { useState } from "react";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON>,
  Di<PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { getCurrentLanguage, getTranslations } from "@/utils/translations";

type FeatureTrioProps = {
  state?: "TX" | "FL" | "NY" | "BE";
};

export default function FeatureTrio({ state = "TX" }: FeatureTrioProps) {
  const [activeFeature, setActiveFeature] = useState<number | null>(null);

  // Get translations based on current language
  const currentLanguage = getCurrentLanguage();
  const t = getTranslations(currentLanguage);

  const features = [
    {
      id: 1,
      code: "01",
      title: t.features.research.title,
      description: t.features.research.description,
      demoTitle: t.features.research.demoTitle,
      demoContent: (
        <div className="flex-grow bg-white rounded-lg p-3 text-gray-800 text-sm overflow-hidden">
          <div className="font-medium mb-2">
            {t.features.research.demoContent.title}
          </div>
          <div className="space-y-2">
            <div className="text-xs border-l-4 border-blue-400 pl-2 bg-blue-50 p-2 rounded">
              <span className="font-medium text-blue-700">
                {t.features.research.demoContent.personalInjuryLabel}
              </span>
              <div className="text-blue-600">
                {t.features.research.demoContent.personalInjuryText}
              </div>
            </div>
            <div className="text-xs border-l-4 border-green-400 pl-2 bg-green-50 p-2 rounded">
              <span className="font-medium text-green-700">
                {t.features.research.demoContent.keyPrecedentLabel}
              </span>
              <div className="text-green-600">
                {t.features.research.demoContent.keyPrecedentText}
              </div>
            </div>
            <div className="text-xs border-l-4 border-purple-400 pl-2 bg-purple-50 p-2 rounded">
              <span className="font-medium text-purple-700">
                {t.features.research.demoContent.relatedCasesLabel}
              </span>
              <div className="text-purple-600">
                {t.features.research.demoContent.relatedCasesText}
              </div>
            </div>
          </div>
        </div>
      ),
    },
    {
      id: 2,
      code: "02",
      title: t.features.drafting.title,
      description: t.features.drafting.description,
      demoTitle: t.features.drafting.demoTitle,
      demoContent: (
        <div className="flex-grow bg-white rounded-lg p-3 text-gray-800 text-sm overflow-hidden">
          <div className="font-medium mb-2">
            {t.features.drafting.demoContent.title}
          </div>
          <p>
            {t.features.drafting.demoContent.petitionText
              .split("Thomas De Smet")
              .map((part, index) =>
                index === 0 ? (
                  part
                ) : (
                  <span key={index}>
                    <span className="bg-[#B8FF5C] bg-opacity-40 px-1">
                      Thomas De Smet
                    </span>
                    {part}
                  </span>
                )
              )}
          </p>
          <div className="mt-2 text-xs bg-yellow-100 border border-yellow-200 p-2 rounded">
            <div className="font-medium text-yellow-800">
              {t.features.drafting.demoContent.aiSuggestionLabel}
            </div>
            <div className="text-yellow-700">
              {t.features.drafting.demoContent.aiSuggestionText}
            </div>
          </div>
        </div>
      ),
    },
    {
      id: 3,
      code: "03",
      title: t.features.receptionist.title,
      description: t.features.receptionist.description,
      demoTitle: t.features.receptionist.demoTitle,
      demoContent: (
        <>
          <div className="relative h-24 mb-4">
            <svg
              className="w-full h-full"
              viewBox="0 0 400 80"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0,40 Q10,10 20,40 T40,40 T60,40 T80,40 T100,40 T120,40 T140,40 T160,40 T180,40 T200,40 T220,40 T240,40 T260,40 T280,40 T300,40 T320,40 T340,40 T360,40 T380,40 T400,40"
                fill="none"
                stroke="white"
                strokeWidth="2"
              />
            </svg>
          </div>
          <div className="text-sm text-white mb-2">
            {t.features.receptionist.sampleConversationLabel}
          </div>
          <div className="text-xs text-white">
            <span className="font-medium">Client:</span>{" "}
            {t.features.receptionist.sampleConversation.client}
            <br />
            <span className="font-medium">AiLex:</span>{" "}
            {t.features.receptionist.sampleConversation.ailex}
          </div>
        </>
      ),
    },
    {
      id: 4,
      code: "04",
      title: t.features.caseOrganizer.title,
      description: t.features.caseOrganizer.description,
      demoTitle: t.features.caseOrganizer.demoTitle,
      demoContent: (
        <div className="flex-grow bg-white rounded-lg p-3 text-gray-800 text-sm overflow-hidden">
          <div className="font-medium mb-2 text-blue-600">
            {t.features.caseOrganizer.demoContent.caseTitle}
          </div>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="bg-green-50 border border-green-200 p-2 rounded">
              <div className="font-medium text-green-800">
                {t.features.caseOrganizer.demoContent.filesLabel}
              </div>
              <div className="text-green-600">
                {t.features.caseOrganizer.demoContent.filesDescription}
              </div>
            </div>
            <div className="bg-yellow-50 border border-yellow-200 p-2 rounded">
              <div className="font-medium text-yellow-800">
                {t.features.caseOrganizer.demoContent.nextDeadlineLabel}
              </div>
              <div className="text-yellow-600">
                {t.features.caseOrganizer.demoContent.nextDeadlineDescription}
              </div>
            </div>
          </div>
          <div className="mt-2 text-xs bg-blue-50 border border-blue-200 p-2 rounded">
            <div className="font-medium text-blue-800">
              {t.features.caseOrganizer.demoContent.latestUpdateLabel}
            </div>
            <div className="text-blue-700">
              {t.features.caseOrganizer.demoContent.latestUpdateDescription}
            </div>
          </div>
        </div>
      ),
    },
    {
      id: 5,
      code: "05",
      title: t.features.workflow.title,
      description: t.features.workflow.description,
      demoTitle: t.features.workflow.demoTitle,
      demoContent: (
        <div className="flex-grow bg-white rounded-lg p-3 text-gray-800 text-sm overflow-hidden">
          <div className="font-medium mb-2">
            {t.features.workflow.demoContent.title}
          </div>
          <div className="space-y-2">
            <div className="flex items-center text-xs">
              <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
              <span className="line-through text-gray-500">
                {t.features.workflow.demoContent.sendWelcomeEmail}
              </span>
            </div>
            <div className="flex items-center text-xs">
              <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
              <span className="line-through text-gray-500">
                {t.features.workflow.demoContent.createClientFile}
              </span>
            </div>
            <div className="flex items-center text-xs">
              <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
              <span className="text-blue-700 font-medium">
                {t.features.workflow.demoContent.scheduleFirstMeeting}
              </span>
            </div>
            <div className="flex items-center text-xs">
              <div className="w-3 h-3 bg-gray-300 rounded-full mr-2"></div>
              <span className="text-gray-400">
                {t.features.workflow.demoContent.collectSupportingDocuments}
              </span>
            </div>
          </div>
          <div className="mt-2 text-xs bg-green-100 border border-green-200 p-2 rounded">
            <span className="text-green-700">
              {t.features.workflow.demoContent.automaticReminder}
            </span>
          </div>
        </div>
      ),
    },
    {
      id: 6,
      code: "06",
      title: t.features.deadlines.title,
      description: t.features.deadlines.description,
      demoTitle: t.features.deadlines.demoTitle,
      demoContent: (
        <div className="flex-grow bg-white rounded-lg p-3 text-gray-800 text-sm overflow-hidden">
          <div className="font-medium mb-2">
            {t.features.deadlines.demoContent.title}
          </div>
          <div className="space-y-2">
            <div className="flex justify-between items-center text-xs border-l-4 border-red-400 pl-2 bg-red-50 p-2 rounded">
              <span className="font-medium text-red-700">
                {t.features.deadlines.demoContent.responseToOpposingBrief}
              </span>
              <span className="text-red-600">
                {t.features.deadlines.demoContent.today}
              </span>
            </div>
            <div className="flex justify-between items-center text-xs border-l-4 border-yellow-400 pl-2 bg-yellow-50 p-2 rounded">
              <span className="font-medium text-yellow-700">
                {t.features.deadlines.demoContent.courtFilingDeadline}
              </span>
              <span className="text-yellow-600">
                {t.features.deadlines.demoContent.threeDays}
              </span>
            </div>
            <div className="flex justify-between items-center text-xs border-l-4 border-green-400 pl-2 bg-green-50 p-2 rounded">
              <span className="font-medium text-green-700">
                {t.features.deadlines.demoContent.clientMeeting}
              </span>
              <span className="text-green-600">
                {t.features.deadlines.demoContent.oneWeek}
              </span>
            </div>
          </div>
          <div className="mt-2 text-xs bg-blue-50 border border-blue-200 p-2 rounded">
            <span className="text-blue-700">
              {t.features.deadlines.demoContent.syncedWithCalendar}
            </span>
          </div>
        </div>
      ),
    },
  ];

  // Feature card variants for animations
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.5,
      },
    }),
  };

  return (
    <section id="features" className="py-16 bg-navy text-white">
      <div className="container-content">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="mb-12 text-center"
        >
          <div className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full px-4 py-2 mb-6">
            <div className="w-2 h-2 bg-[#B8FF5C] rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-white">
              {t.featuresCoreFeatures}
            </span>
          </div>
          <h2 className="text-3xl font-bold text-center">
            {t.featuresMainTitle}
          </h2>
        </motion.div>

        <div className="grid md:grid-cols-3 gap-8 items-stretch">
          {features.map((feature, index) => (
            <motion.div
              key={feature.id}
              custom={index}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={cardVariants}
              className="feature-card group flex flex-col h-full"
              onMouseEnter={() => setActiveFeature(feature.id)}
              onMouseLeave={() => setActiveFeature(null)}
            >
              <div className="code text-xs text-gray-400 mb-2">
                {feature.code}
              </div>
              <h3 className="font-bold text-xl mb-3">{feature.title}</h3>
              <div className="absolute top-6 right-6 code text-xs bg-[#B8FF5C] rounded-full px-2 py-1 text-navy">
                {t.featuresReady}
              </div>

              <p className="text-gray-300 mb-8">{feature.description}</p>

              <div className="mt-auto flex items-center text-primary group-hover:text-[#B8FF5C] transition-default">
                <span>{t.featuresSeeAction}</span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 ml-2 group-hover:translate-x-1 transition-default"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>

              <Dialog>
                <DialogTrigger asChild>
                  <div
                    className={`absolute inset-0 bg-primary bg-opacity-90 p-6 flex flex-col opacity-0 transform translate-y-4 transition-default cursor-pointer ${
                      activeFeature === feature.id
                        ? "opacity-100 translate-y-0"
                        : ""
                    }`}
                  >
                    <div className="code text-xs text-navy mb-2">
                      {feature.demoTitle}
                    </div>
                    <div className="flex-grow flex flex-col justify-center">
                      {feature.demoContent}
                    </div>
                  </div>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[650px] max-h-[80vh] overflow-y-auto bg-navy text-white">
                  <DialogHeader>
                    <DialogTitle>{feature.demoTitle}</DialogTitle>
                  </DialogHeader>
                  <div className="p-4">
                    <div className="aspect-video bg-navy bg-opacity-50 rounded-md flex flex-col items-center justify-center p-6 border border-gray-700">
                      {feature.demoContent}
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </motion.div>
          ))}
        </div>

        <motion.div
          className="text-center mt-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <p className="text-base text-[#B8FF5C] text-center">
            <strong>{t.featuresTrainingRequired}</strong>
            <br />
            <em>{t.featuresEasyBreezyReady}</em>
          </p>
        </motion.div>
      </div>
    </section>
  );
}
