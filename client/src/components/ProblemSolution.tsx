import { motion, useAnimation } from "framer-motion";
import { useEffect, useRef } from "react";
import hourglassIcon from "../assets/hourglass-new-icon.png";
import sadFaceIcon from "../assets/sad-face-icon.png";
import dollarSignIcon from "../assets/dollar-icon-new.png";
import TypewriterCTA from "./TypewriterCTA";
// Import modern flat-3D icons
import timeMoneyIllustration from "@assets/ChatGPT Image Apr 26, 2025, 06_34_08 PM.png";
import missedCallIllustration from "@assets/ChatGPT Image Apr 26, 2025, 06_52_11 PM.png";
import priceLockIllustration from "@assets/ChatGPT Image Apr 26, 2025, 06_48_01 PM.png";
import { getCurrentLanguage, getTranslations } from "@/utils/translations";

export default function ProblemSolution() {
  const solutionCardRef = useRef<HTMLDivElement>(null);
  const buttonAnimation = useAnimation();

  // Get translations based on current language
  const currentLanguage = getCurrentLanguage();
  const t = getTranslations(currentLanguage);

  // Set up the pulsing animation for the button with a simpler approach
  useEffect(() => {
    let isMounted = true;

    const animateButton = async () => {
      while (isMounted) {
        try {
          await buttonAnimation.start({
            scale: 1.05,
            boxShadow: "0px 0px 15px rgba(100, 100, 255, 0.3)",
            transition: { duration: 0.7, ease: "easeInOut" },
          });

          if (!isMounted) break;

          await buttonAnimation.start({
            scale: 1,
            boxShadow: "0px 0px 0px rgba(100, 100, 255, 0)",
            transition: { duration: 0.7, ease: "easeInOut" },
          });

          if (!isMounted) break;

          // Wait 5 seconds before next pulse
          await new Promise((resolve) => setTimeout(resolve, 5000));
        } catch (error) {
          // Animation cancelled, exit loop
          break;
        }
      }
    };

    // Start the animation immediately
    const timer = setTimeout(() => {
      animateButton();
    }, 1000);

    return () => {
      isMounted = false;
      clearTimeout(timer);
      buttonAnimation.stop();
    };
  }, [buttonAnimation]);

  const problems = [
    {
      emoji: "⏳",
      title: t.problems.unbilledTime.title,
      description: t.problems.unbilledTime.description,
    },
    {
      emoji: "😰",
      title: t.problems.missedCalls.title,
      description: t.problems.missedCalls.description,
    },
    {
      emoji: "💸",
      title: t.problems.expensiveSoftware.title,
      description: t.problems.expensiveSoftware.description,
    },
  ];

  return (
    <section
      id="features"
      className="relative bg-blue-50 py-20 overflow-hidden"
    >
      {/* Subtle dot pattern background */}
      <div className="absolute inset-0 opacity-5">
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: "radial-gradient(#3b82f6 1px, transparent 1px)",
            backgroundSize: "30px 30px",
          }}
        ></div>
      </div>
      {/* Soft gradient background overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/30 to-blue-100/30"></div>
      <div className="max-w-6xl mx-auto px-6 relative z-10">
        <section className="w-full text-center py-12 mb-8">
          <div className="inline-flex items-center gap-2 bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-full px-4 py-2 mb-6">
            <div className="w-2 h-2 bg-indigo-400 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-gray-700">
              {t.solutionSectionSubtitle}
            </span>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            {t.solutionSectionTitle}
          </h1>
          <p className="text-lg text-gray-600 leading-relaxed max-w-2xl mx-auto">
            {t.solutionSectionDescription}{" "}
            <span className="inline-block bg-blue-500 text-white px-3 py-1 rounded-md font-bold">
              {t.solutionSectionHighlight}
            </span>
          </p>
          <p className="text-lg text-gray-600 leading-relaxed max-w-2xl mx-auto mt-8 mb-2">
            <strong>{t.solutionSectionBenefits}</strong>
          </p>
        </section>

        <div className="relative px-4 mb-16">
          {/* Gradient blobs for background effect */}
          <div className="absolute top-[-80px] left-[-80px] w-[320px] h-[320px] rounded-full z-[-1] bg-[radial-gradient(circle,_rgba(184,255,92,0.25)_0%,_transparent_70%)] blur-3xl animate-float-slow" />
          <div
            className="absolute bottom-[-80px] right-[-80px] w-[320px] h-[320px] rounded-full z-[-1] bg-[radial-gradient(circle,_rgba(184,255,92,0.28)_0%,_transparent_70%)] blur-3xl animate-float-slow"
            style={{ animationDelay: "2s" }}
          />

          {/* First row: 3 cards side by side */}
          <div className="flex flex-wrap justify-center md:justify-between gap-6 mb-8">
            {/* Card 1 - Voice Agent / Client Intake */}
            <motion.div
              className="bg-white rounded-3xl p-6 shadow-xl flex flex-col text-left hover:shadow-2xl transition w-[320px] card-1 relative"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              whileHover={{
                scale: 1.01,
                boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)",
              }}
              transition={{ duration: 0.3 }}
            >
              {/* Step Label */}
              <div className="absolute -top-3 left-6 bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-1 rounded-full text-sm font-semibold shadow-lg">
                {t.solutionSteps.step1}
              </div>

              {/* Solution Headline */}
              <div className="mb-6 pt-4">
                <h3 className="text-lg font-bold text-gray-900 leading-tight mb-3">
                  {t.solutionHeadlines.headline1}
                </h3>
                <div className="flex justify-start">
                  <span className="inline-flex items-center text-xs font-medium px-2 py-0.5 rounded-full bg-blue-100 text-blue-700">
                    {t.solutionTimeSavings.badge1}
                  </span>
                </div>
              </div>

              {/* Description */}
              <div className="mb-4">
                <p className="text-sm text-gray-600 leading-relaxed">
                  {t.solutionDescriptions.description1}
                </p>
              </div>

              {/* Top Intake UI */}
              <div className="border border-blue-100 rounded-md p-4 mb-4">
                <div className="flex justify-between items-center mb-2">
                  <h2 className="text-blue-800 font-semibold">
                    {t.solutionDemoLabels.newClientCall}
                  </h2>
                  <span className="inline-flex items-center justify-center text-xs font-medium px-2 py-0.5 rounded-full bg-green-100 text-green-700 text-center">
                    {t.solutionDemoLabels.callHandledByAiLex}
                  </span>
                </div>
                <p className="italic text-sm text-gray-700">
                  {t.solutionClientQuote}
                </p>
                <div className="text-xs text-blue-600 mt-2 flex items-center justify-between">
                  <span>{t.solutionDemoLabels.capturedAutomatically}</span>
                  <div className="flex items-center">
                    {/* Simplified waveform */}
                    <div className="flex items-center gap-[1px] h-3 w-16 mr-1">
                      <div className="h-1 w-1 rounded-full bg-blue-400 opacity-80"></div>
                      <div className="h-2 w-1 rounded-full bg-blue-500 opacity-80 animate-pulse"></div>
                      <div className="h-3 w-1 rounded-full bg-blue-600 opacity-80 animate-[pulse_1.3s_ease-in-out_infinite]"></div>
                      <div className="h-2 w-1 rounded-full bg-blue-500 opacity-80 animate-[pulse_0.7s_ease-in-out_infinite]"></div>
                      <div className="h-3 w-1 rounded-full bg-blue-600 opacity-80 animate-[pulse_1s_ease-in-out_infinite]"></div>
                      <div className="h-1 w-1 rounded-full bg-blue-400 opacity-80"></div>
                    </div>
                    <span>{t.solutionDemoLabels.voiceRecording}</span>
                  </div>
                </div>
              </div>

              {/* Captured Intake Summary */}
              <div className="bg-blue-50 p-3 rounded-md text-sm mb-4">
                <p>
                  <strong>{t.solutionDemoLabels.clientName}</strong> Daniel
                </p>
                <p>
                  <strong>{t.solutionDemoLabels.caseType}</strong>{" "}
                  {currentLanguage === "fr"
                    ? "Litige locatif"
                    : currentLanguage === "nl"
                      ? "Huurgeschil"
                      : "Tenancy Dispute"}
                </p>
                <p>
                  <strong>{t.solutionDemoLabels.urgency}</strong>{" "}
                  {currentLanguage === "fr"
                    ? "Souhaite un rappel aujourd'hui"
                    : currentLanguage === "nl"
                      ? "Wil vandaag teruggebeld worden"
                      : "Wants callback today"}
                </p>
                <div className="mt-2">
                  <span className="inline-flex items-center justify-center text-xs font-medium px-2 py-0.5 rounded-full bg-green-100 text-green-700">
                    {t.solutionDemoLabels.syncedToAiLex}
                  </span>
                </div>
              </div>

              {/* Testimonial Quote */}
              <div className="mt-6 pt-4 border-t border-gray-100">
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-xl px-4 py-3 text-sm text-gray-700 italic relative">
                  <p className="mb-0">{t.solutionTestimonialQuote}</p>
                </div>
              </div>
            </motion.div>

            {/* Card 2 - Admin Dashboard - Premium Framer Style */}
            <motion.div
              className="bg-gradient-to-br from-white to-slate-50 rounded-3xl shadow-xl p-6 hover:shadow-2xl transition-all duration-300 relative flex flex-col justify-between w-[320px] card-2"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              whileHover={{
                scale: 1.01,
                boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)",
              }}
              transition={{ duration: 0.3, delay: 0.1 }}
            >
              {/* Step Label */}
              <div className="absolute -top-3 left-6 bg-gradient-to-r from-indigo-500 to-purple-600 text-white px-4 py-1 rounded-full text-sm font-semibold shadow-lg">
                {t.solutionSteps.step2}
              </div>

              {/* Solution Headline */}
              <div className="mb-4 pt-4">
                <h3 className="text-lg font-bold text-gray-900 leading-tight mb-3">
                  {t.solutionHeadlines.headline2}
                </h3>
                <div className="flex justify-start mb-4">
                  <span className="inline-flex items-center text-xs font-medium px-2 py-0.5 rounded-full bg-indigo-100 text-indigo-700">
                    {t.solutionTimeSavings.badge2}
                  </span>
                </div>
                <p className="text-sm text-gray-600 leading-relaxed">
                  {t.solutionDescriptions.description2}
                </p>
              </div>

              {/* Mini UI */}
              <motion.div
                className="bg-white rounded-xl shadow-md p-4 space-y-3 text-sm font-medium text-gray-700 hover:shadow-lg transition-shadow duration-300"
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                variants={{
                  hidden: { opacity: 0, y: 10 },
                  visible: { opacity: 1, y: 0, transition: { duration: 0.4 } },
                }}
              >
                <div className="flex justify-between items-center">
                  <span>📂 {t.solutionCaseName}</span>
                  <motion.span
                    className="inline-flex items-center justify-center text-center text-xs font-medium px-2 py-0.5 rounded-full bg-indigo-100 text-indigo-700"
                    animate={{ opacity: [0.85, 1, 0.85] }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                  >
                    {t.aiPrioritized}
                  </motion.span>
                </div>
                <div className="flex justify-between items-center">
                  <span>📝 {t.noticeOfDisputeDraft}</span>
                  <span className="inline-flex items-center justify-center text-center text-xs font-medium px-2 py-0.5 rounded-full bg-green-100 text-green-700">
                    {t.dueToday}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span>📎 {t.medicalCertificate}</span>
                  <span className="inline-flex items-center justify-center text-xs font-medium px-2 py-0.5 rounded-full bg-gray-100 text-gray-500">
                    {t.uploaded}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span>📌 {t.setCourtDate}</span>
                  <span className="inline-flex items-center justify-center text-center text-xs font-medium px-2 py-0.5 rounded-full bg-yellow-100 text-yellow-800">
                    {t.highPriority}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span>✏️ {t.responseLetter}</span>
                  <span className="inline-flex items-center justify-center text-xs font-medium px-2 py-0.5 rounded-full bg-blue-100 text-blue-700 animate-pulse text-center">
                    {t.aiSuggested}
                  </span>
                </div>
              </motion.div>

              {/* Testimonial Quote */}
              <div className="mt-6 pt-4 border-t border-gray-100">
                <div className="bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-100 rounded-xl px-4 py-3 text-sm text-gray-700 italic relative">
                  <p className="mb-0">{t.organizationTestimonial}</p>
                </div>
              </div>
            </motion.div>

            {/* Card 3 - AI Legal Research & Drafting */}
            <motion.div
              className="bg-gradient-to-br from-white to-blue-50 rounded-3xl shadow-xl p-6 hover:shadow-2xl transition-all duration-300 relative flex flex-col justify-between w-[320px] card-3"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              whileHover={{
                scale: 1.01,
                boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)",
              }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              {/* Step Label */}
              <div className="absolute -top-3 left-6 bg-gradient-to-r from-emerald-500 to-green-600 text-white px-4 py-1 rounded-full text-sm font-semibold shadow-lg">
                {t.solutionSteps.step3}
              </div>

              {/* Solution Headline */}
              <div className="mb-4 pt-4">
                <h3 className="text-lg font-bold text-gray-900 leading-tight mb-3">
                  {t.solutionHeadlines.headline3}
                </h3>
                <div className="flex justify-start mb-4">
                  <span className="inline-flex items-center text-xs font-medium px-2 py-0.5 rounded-full bg-emerald-100 text-emerald-700">
                    {t.solutionTimeSavings.badge3}
                  </span>
                </div>
                <p className="text-sm text-gray-600 leading-relaxed">
                  {t.solutionDescriptions.description3}
                </p>
              </div>

              {/* Top-right emoji badge */}
              <div className="absolute top-3 right-3 text-xl opacity-10">
                ⚖️
              </div>

              {/* Mini UI */}
              <motion.div
                className="bg-white rounded-xl shadow-md p-4 space-y-3 text-sm font-medium text-gray-700 hover:shadow-lg transition-shadow duration-300"
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                variants={{
                  hidden: { opacity: 0, y: 10 },
                  visible: { opacity: 1, y: 0, transition: { duration: 0.4 } },
                }}
              >
                <div className="flex justify-between items-center">
                  <span>{t.solutionCaseName}</span>
                  <div className="flex items-center gap-2">
                    <span className="inline-flex items-center text-xs font-medium px-2 py-0.5 rounded-full bg-purple-50 text-purple-700 border border-purple-200 shadow-sm text-center">
                      {t.legalResearchLabels.cited17x}
                    </span>
                    <span className="inline-flex items-center text-xs font-medium px-2 py-0.5 rounded-full bg-blue-50 text-blue-700 border border-blue-200 shadow-sm text-center">
                      {t.legalResearchLabels.belgianLaw}
                    </span>
                  </div>
                </div>

                <div className="flex justify-between items-center">
                  <span>{t.legalResearchLabels.draftClientMemo}</span>
                  <span className="inline-flex items-center text-xs font-medium px-2 py-0.5 rounded-full bg-indigo-50 text-indigo-700 border border-indigo-200 shadow-sm animate-pulse text-center">
                    {t.aiSuggested}
                  </span>
                </div>

                <div className="flex justify-between items-center">
                  <span>{t.legalResearchLabels.civilCodeArt}</span>
                  <span className="inline-flex items-center text-xs font-medium px-2 py-0.5 rounded-full bg-green-50 text-green-700 border border-green-200 shadow-sm">
                    {t.legalResearchLabels.relevant}
                  </span>
                </div>
              </motion.div>

              {/* Testimonial Quote */}
              <div className="mt-6 pt-4 border-t border-gray-100">
                <div className="bg-gradient-to-r from-emerald-50 to-green-50 border border-emerald-100 rounded-xl px-4 py-3 text-sm text-gray-700 italic relative">
                  <p className="mb-0">{t.researchTestimonial}</p>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Second row: Full-width pricing card */}
          <div className="w-full mt-12">
            {/* Card 4 - Pricing Comparison - Full Width Banner */}
            <motion.div
              className="bg-gradient-to-br from-white to-blue-50 rounded-3xl shadow-xl p-8 hover:shadow-2xl transition-all duration-300 relative flex flex-col justify-between w-full"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              whileHover={{
                scale: 1.005,
                boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)",
              }}
              transition={{ duration: 0.3, delay: 0.3 }}
            >
              <div className="flex flex-col md:flex-row gap-8 justify-between">
                <div className="flex-1">
                  {/* Headline */}
                  <div className="mb-4">
                    <h3 className="text-2xl font-semibold text-gray-900 leading-snug mb-2">
                      {t.bigLawTechTitle}
                    </h3>
                    <p className="text-gray-500 mt-1">
                      {t.bigLawTechDescription}
                    </p>
                  </div>

                  {/* Testimonial */}
                  <div className="mt-6 bg-gray-50 border border-gray-100 rounded-xl px-4 py-3 text-sm text-gray-700 italic">
                    "AiLex saved us over €5,000 this year."
                  </div>
                </div>

                <div className="flex-1 max-w-xl">
                  {/* Pricing Comparison */}
                  <div className="space-y-5 text-sm font-medium text-gray-800">
                    {/* Enterprise Tier */}
                    <div>
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-gray-600">
                          {t.enterpriseLegalTech}
                        </span>
                        <div className="flex items-center gap-2">
                          <span className="inline-flex items-center text-xs font-medium px-2 py-0.5 rounded-full bg-blue-100 text-blue-700">
                            {t.oneUser}
                          </span>
                          <span className="font-semibold">{t.price199}</span>
                        </div>
                      </div>
                      <div className="w-full h-3 rounded-full bg-gray-100 overflow-hidden">
                        <motion.div
                          className="h-3 bg-yellow-400 rounded-full"
                          initial={{ width: "0%" }}
                          whileInView={{ width: "95%" }}
                          transition={{ duration: 1, ease: "easeOut" }}
                          viewport={{ once: true }}
                        />
                      </div>
                    </div>

                    {/* AiLex Tier */}
                    <div>
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-gray-600">{t.ailexTeamPlan}</span>
                        <div className="flex items-center gap-2">
                          <span className="inline-flex items-center text-xs font-medium px-2 py-0.5 rounded-full bg-blue-100 text-blue-700">
                            {t.twoUsers}
                          </span>
                          <span className="font-semibold">{t.price99}</span>
                        </div>
                      </div>
                      <div className="w-full h-3 rounded-full bg-gray-100 overflow-hidden">
                        <motion.div
                          className="h-3 bg-blue-600 rounded-full"
                          initial={{ width: "0%" }}
                          whileInView={{ width: "28%" }}
                          transition={{
                            duration: 1,
                            ease: "easeOut",
                            delay: 0.3,
                          }}
                          viewport={{ once: true }}
                        />
                      </div>
                    </div>

                    {/* Cost savings */}
                    <div className="flex justify-center mt-2">
                      <motion.span
                        className="inline-flex items-center text-xs font-medium px-2 py-0.5 rounded-full bg-blue-100 text-blue-700"
                        initial={{ opacity: 0 }}
                        whileInView={{ opacity: 1 }}
                        transition={{ duration: 0.5, delay: 1.2 }}
                        viewport={{ once: true }}
                      >
                        {t.costSavings}
                      </motion.span>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Typewriter Call to Action */}
        <TypewriterCTA />
      </div>
    </section>
  );
}
