// Language detection and redirection utilities for Belgian site

export type BelgianLanguage = "en" | "fr" | "nl";

export interface LanguagePreference {
  language: BelgianLanguage;
  source: "cookie" | "geolocation" | "browser" | "fallback";
}

// Cookie management
export const LANGUAGE_COOKIE_NAME = "ailex_lang";
export const COOKIE_EXPIRY_DAYS = 365;

export function setLanguageCookie(language: BelgianLanguage): void {
  const expiryDate = new Date();
  expiryDate.setDate(expiryDate.getDate() + COOKIE_EXPIRY_DAYS);

  document.cookie = `${LANGUAGE_COOKIE_NAME}=${language}; expires=${expiryDate.toUTCString()}; path=/; SameSite=Lax`;
}

export function getLanguageCookie(): BelgianLanguage | null {
  if (typeof document === "undefined") return null;

  const cookies = document.cookie.split(";");
  const langCookie = cookies.find((cookie) =>
    cookie.trim().startsWith(`${LANGUAGE_COOKIE_NAME}=`)
  );

  if (langCookie) {
    const language = langCookie.split("=")[1].trim() as BelgianLanguage;
    if (["en", "fr", "nl"].includes(language)) {
      return language;
    }
  }

  return null;
}

// Browser language detection
export function getBrowserLanguage(): BelgianLanguage | null {
  if (typeof navigator === "undefined") return null;

  const browserLang = navigator.language.toLowerCase();

  // Check for exact matches first
  if (browserLang.startsWith("nl")) return "nl";
  if (browserLang.startsWith("fr")) return "fr";
  if (browserLang.startsWith("en")) return "en";

  // Check secondary languages
  const languages = navigator.languages || [navigator.language];
  for (const lang of languages) {
    const lowerLang = lang.toLowerCase();
    if (lowerLang.startsWith("nl")) return "nl";
    if (lowerLang.startsWith("fr")) return "fr";
    if (lowerLang.startsWith("en")) return "en";
  }

  return null;
}

// Geolocation-based language detection (simplified)
export async function getGeolocationLanguage(): Promise<BelgianLanguage | null> {
  try {
    // Use a free IP geolocation service with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 3000);

    const response = await fetch("https://ipapi.co/json/", {
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (!response.ok) return null;

    const data = await response.json();

    // Check if user is in Belgium
    if (data.country_code === "BE") {
      // Rough regional detection based on city/region
      const region = data.region?.toLowerCase() || "";
      const city = data.city?.toLowerCase() || "";

      // Flanders (Dutch-speaking regions)
      const flemishRegions = [
        "flanders",
        "vlaanderen",
        "antwerp",
        "antwerpen",
        "ghent",
        "gent",
        "bruges",
        "brugge",
        "leuven",
        "hasselt",
        "mechelen",
      ];
      if (flemishRegions.some((r) => region.includes(r) || city.includes(r))) {
        return "nl";
      }

      // Wallonia (French-speaking regions)
      const walloonRegions = [
        "wallonia",
        "wallonie",
        "liège",
        "liege",
        "namur",
        "charleroi",
        "mons",
        "tournai",
      ];
      if (walloonRegions.some((r) => region.includes(r) || city.includes(r))) {
        return "fr";
      }

      // Brussels is bilingual, so we'll rely on browser language
      if (
        region.includes("brussels") ||
        city.includes("brussels") ||
        city.includes("bruxelles")
      ) {
        return getBrowserLanguage();
      }
    }

    return null;
  } catch (error) {
    console.warn("Geolocation detection failed:", error);
    return null;
  }
}

// Main language detection function
export async function detectPreferredLanguage(): Promise<LanguagePreference> {
  // 1. Check for existing cookie preference
  const cookieLanguage = getLanguageCookie();
  if (cookieLanguage) {
    return { language: cookieLanguage, source: "cookie" };
  }

  // 2. Try geolocation detection
  try {
    const geoLanguage = await getGeolocationLanguage();
    if (geoLanguage) {
      return { language: geoLanguage, source: "geolocation" };
    }
  } catch (error) {
    console.warn("Geolocation detection failed:", error);
  }

  // 3. Fall back to browser language
  const browserLanguage = getBrowserLanguage();
  if (browserLanguage) {
    return { language: browserLanguage, source: "browser" };
  }

  // 4. Ultimate fallback to English
  return { language: "en", source: "fallback" };
}

// Route mapping utilities
export function getLanguageFromPath(pathname: string): BelgianLanguage | null {
  if (pathname.startsWith("/be-fr")) return "fr";
  if (pathname.startsWith("/be-nl")) return "nl";
  if (pathname.startsWith("/be") || pathname === "/be") return "en";
  return null;
}

export function getRouteForLanguage(
  currentPath: string,
  targetLanguage: BelgianLanguage
): string {
  // Remove language prefix from current path
  let basePath = currentPath;
  if (basePath.startsWith("/be-fr")) {
    basePath = basePath.replace("/be-fr", "");
  } else if (basePath.startsWith("/be-nl")) {
    basePath = basePath.replace("/be-nl", "");
  } else if (basePath.startsWith("/be")) {
    basePath = basePath.replace("/be", "");
  }

  // Ensure basePath starts with /
  if (!basePath.startsWith("/")) {
    basePath = "/" + basePath;
  }

  // Map to target language
  switch (targetLanguage) {
    case "fr":
      return "/be-fr" + basePath;
    case "nl":
      return "/be-nl" + basePath;
    case "en":
    default:
      return "/be" + basePath;
  }
}

// Language display names
export const LANGUAGE_NAMES: Record<BelgianLanguage, string> = {
  en: "English",
  fr: "Français",
  nl: "Nederlands",
};

// Language flags/icons
export const LANGUAGE_FLAGS: Record<BelgianLanguage, string> = {
  en: "🇬🇧",
  fr: "🇫🇷",
  nl: "🇳🇱",
};
